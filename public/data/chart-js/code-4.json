&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/chart.js/dist/chart.umd.js"/&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;canvas id="polarAreaChart"&gt;&lt;/canvas&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  var ctx4 = document.getElementById('polarAreaChart');
  var polarAreaChart = new Chart(ctx4, {
    type: 'polarArea',
    data: {
      datasets: [{
        data: [11, 16, 7, 3, 14],
        backgroundColor: ['rgba('+ app.color.themeRgb +', .5)', 'rgba('+ app.color.secondaryRgb +', .5)', 'rgba('+ app.color.themeRgb +', .25)', 'rgba('+ app.color.themeRgb +', .75)', 'rgba('+ app.color.secondaryRgb +', .75)'],
        borderWidth: 0
      }],
      labels: ['IE', 'Safari', 'Chrome', 'Firefox', 'Opera']
    }
  });
&lt;/script&gt;