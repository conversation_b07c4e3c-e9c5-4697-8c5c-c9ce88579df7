&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/apexcharts/dist/apexcharts.min.js"/&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;div id="apexMixedChart"&gt;&lt;/div&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  var apexMixedChartOptions = {
    chart: {
      height: 350,
      type: 'line',
      stacked: false
    },
    dataLabels: { enabled: false },
    series: [
      { name: 'Income', type: 'column', data: [1.4, 2, 2.5, 1.5, 2.5, 2.8, 3.8, 4.6] },
      { name: 'Cashflow', type: 'column', data: [1.1, 3, 3.1, 4, 4.1, 4.9, 6.5, 8.5] }, 
      { name: 'Revenue', type: 'line', data: [20, 29, 37, 36, 44, 45, 50, 58] }
    ],
    stroke: { width: [0, 0, 3] },
    colors: [app.color.theme, 'rgba('+ app.color.bodyColorRgb + ', .5)', app.color.warning],
    title: {
      text: 'XYZ - Stock Analysis (2012 - 2021)',
      align: 'left',
      offsetX: 110
    },
    xaxis: {
      categories: [2013, 2014, 2015, 2016, 2017, 2018, 2019, 2021, 2021],
      axisBorder: {
        show: true,
        color: app.color.borderColor,
        height: 1,
        width: '100%',
        offsetX: 0,
        offsetY: -1
      },
      axisTicks: {
        show: true,
        borderType: 'solid',
        color: app.color.borderColor,
        height: 6,
        offsetX: 0,
        offsetY: 0
      }
    },
    yaxis: [{
      axisTicks: { show: true, color: app.color.borderColor },
      axisBorder: { show: true, color: app.color.theme },
      labels: { style: { colors: [app.color.bodyColor] } },
      title: {
        text: "Income (thousand crores)",
        style: { color: app.color.theme }
      },
      tooltip: { enabled: true }
    },{
      seriesName: 'Income',
      opposite: true,
      axisTicks: { show: true, color: app.color.borderColor },
      axisBorder: { show: true, color: app.color.borderColor },
      labels: { style: { colors: [app.color.bodyColor] } },
      title: {
        text: "Operating Cashflow (thousand crores)",
        style: { color: app.color.bodyColor }
      },
    }, {
      seriesName: 'Revenue',
      opposite: true,
      axisTicks: { show: true, color: app.color.borderColor },
      axisBorder: { show: true, color: app.color.warning },
      labels: { style: { colors: [app.color.bodyColor] } },
      title: {
        text: "Revenue (thousand crores)",
        style: { color: app.color.warning }
      }
    }],
    tooltip: {
      fixed: {
        enabled: true,
        position: 'topLeft',
        offsetY: 30,
        offsetX: 60
      },
    },
    legend: { horizontalAlign: 'left', offsetX: 40 }
  };
  var apexMixedChart = new ApexCharts(
    document.querySelector('#apexMixedChart'),
    apexMixedChartOptions
  );
  apexMixedChart.render();
&lt;/script&gt;