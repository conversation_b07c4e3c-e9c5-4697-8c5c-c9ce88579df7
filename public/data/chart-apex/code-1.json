&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/apexcharts/dist/apexcharts.min.js"/&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;div id="apexLineChart"&gt;&lt;/div&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  var apexLineChartOptions = {
    chart: {
      height: 350,
      type: 'line',
      toolbar: { show: false }
    },
    title: {
      text: 'Average High & Low Temperature',
      align: 'center',
      color: app.color.bodyColor
    },
    colors: [app.color.theme, app.color.secondary],
    dataLabels: {
      enabled: true,
      offsetY: 1,
      background: {
        enabled: true,
        padding: 4,
        borderRadius: 4,
        borderWidth: 0,
        opacity: 0.9,
        dropShadow: { enabled: false }
      },
      style: {
        fontSize: '11px',
        fontFamily: app.font.bodyFontFamily,
        fontWeight: 500
      }
    },
    stroke: { curve: 'smooth', width: 3 },
    grid: {
      row: {
        colors: [app.color.bodyBg, 'transparent'],
        opacity: 0.5
      }
    },
    series: [
      { name: 'High - 2021', data: [28, 29, 33, 36, 32, 32, 33] }, 
      { name: 'Low - 2021', data: [12, 11, 14, 18, 17, 13, 13] }
    ],
    markers: { size: 4 },
    xaxis: { categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'] },
    yaxis: { min: 5, max: 40 },
    legend: {
      show: true,
      position: 'top',
      offsetY: -10,
      horizontalAlign: 'right',
      floating: true
    }
  };
  var apexLineChart = new ApexCharts(
    document.querySelector('#apexLineChart'),
    apexLineChartOptions
  );
  apexLineChart.render();
&lt;/script&gt;