&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/apexcharts/dist/apexcharts.min.js"/&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;div id="apexPieChart"&gt;&lt;/div&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  var apexPieChartOptions = {
    chart: {
      height: 365,
      type: 'pie',
    },
    dataLabels: {
      dropShadow: {
        enabled: false,
        top: 1,
        left: 1,
        blur: 1,
        opacity: 1
      }
    },
    stroke: { show: false },
    colors: [ 'rgba('+ app.color.pinkRgb +', .75)',  'rgba('+ app.color.warningRgb +', .75)',  'rgba('+app.color.themeRgb +', .75)', 'rgba('+ app.color.bodyColorRgb + ', .5)',  'rgba('+app.color.indigoRgb +', .75)'],
    labels: ['Team A', 'Team B', 'Team C', 'Team D', 'Team E'],
    series: [44, 55, 13, 43, 22],
    title: { text: 'HeatMap Chart (Single color)' }
  };
  var apexPieChart = new ApexCharts(
    document.querySelector('#apexPieChart'),
    apexPieChartOptions
  );
  apexPieChart.render();
&lt;/script&gt;