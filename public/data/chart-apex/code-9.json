&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/apexcharts/dist/apexcharts.min.js"/&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;div id="apexHeatmapChart"&gt;&lt;/div&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  function generateHeatmapData(count, yrange) {
    var i = 0;
    var series = [];
    while (i < count) {
      var x = 'w' + (i + 1).toString();
      var y = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;

      series.push({ x: x, y: y });
      i++;
    }
    return series;
  }
  var apexHeatmapChartOptions = {
    chart: {
      height: 350,
      type: 'heatmap',
    },
    dataLabels: { enabled: false },
    colors: [app.color.theme],
    series: [
      { name: '<PERSON>ric1', data: generateHeatmapData(18, {  min: 0, max: 90 }) }, 
      { name: 'Metric2', data: generateHeatmapData(18, {  min: 0, max: 90 }) }, 
      { name: 'Metric3', data: generateHeatmapData(18, {  min: 0, max: 90 }) }, 
      { name: 'Metric4', data: generateHeatmapData(18, {  min: 0, max: 90 }) }, 
      { name: 'Metric5', data: generateHeatmapData(18, {  min: 0, max: 90 }) }, 
      { name: 'Metric6', data: generateHeatmapData(18, {  min: 0, max: 90 }) },
      { name: 'Metric7', data: generateHeatmapData(18, {  min: 0, max: 90 }) }, 
      { name: 'Metric8', data: generateHeatmapData(18, {  min: 0, max: 90 }) }, 
      { name: 'Metric9', data: generateHeatmapData(18, {  min: 0, max: 90 }) }
    ],
    title: { text: 'HeatMap Chart (Single color)' }
  }
  var apexHeatmapChart = new ApexCharts(
    document.querySelector('#apexHeatmapChart'),
    apexHeatmapChartOptions
  );
  apexHeatmapChart.render();
&lt;/script&gt;