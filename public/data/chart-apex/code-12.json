&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/apexcharts/dist/apexcharts.min.js"/&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;div id="apexRadarChart"&gt;&lt;/div&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  var apexRadarChartOptions = {
    chart: {
      height: 320,
      type: 'radar',
    },
    series: [
      { name: 'Series 1', data: [20, 100, 40, 30, 50, 80, 33] }
    ],
    labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
    plotOptions: {
      radar: {
        size: 140,
        polygons: {
          strokeColors: app.color.borderColor,
          strokeWidth: 1,
          connectorColors: app.color.borderColor,
          fill: {
            colors: ['rgba('+app.color.bodyColorRgb +', .05)', 'rgba('+ app.color.bodyColorRgb + ', .05)']
          }
        }
      }
    },
    title: { text: 'Radar with Polygon Fill' },
    colors: [app.color.theme],
    markers: {
      size: 4,
      colors: [app.color.theme],
      strokeColor: app.color.theme,
      strokeWidth: 2,
    },
    tooltip: {
      y: { formatter: function(val) { return val } }
    },
    yaxis: {
      tickAmount: 7,
      labels: {
        formatter: function(val, i) {
          return (i % 2 === 0) ? val : '';
        }
      }
    }
  }
  var apexRadarChart = new ApexCharts(
    document.querySelector('#apexRadarChart'),
    apexRadarChartOptions
  );
  apexRadarChart.render();
&lt;/script&gt;