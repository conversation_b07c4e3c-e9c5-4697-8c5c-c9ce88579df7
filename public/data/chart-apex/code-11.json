&lt;!-- required js --&gt;
&lt;script src="/assets/plugins/apexcharts/dist/apexcharts.min.js"/&gt;&lt;/script&gt;

&lt;!-- html --&gt;
&lt;div id="apexRadialBarChart"&gt;&lt;/div&gt;

&lt;!-- script --&gt;
&lt;script&gt;
  var apexRadialBarChartOptions = {
    chart: {
      height: 350,
      type: 'radialBar'
    },
    plotOptions: {
      radialBar: {
        offsetY: 0,
        startAngle: 0,
        endAngle: 270,
        hollow: {
          margin: 5,
          size: '30%',
          background: 'transparent',
          image: undefined,
        },
        track: { background: app.color.borderColor },
        dataLabels: {
          name: { show: false },
          value: { show: false }
        }
      }
    },
    colors: [app.color.cyan, app.color.theme, app.color.indigo, app.color.gray300],
    series: [76, 67, 61, 90],
    labels: ['Vimeo', 'Messenger', 'Facebook', 'LinkedIn'],
    legend: {
      show: true,
      floating: true,
      position: 'left',
      labels: { useSeriesColors: true },
      markers: { size: 0 },
      formatter: function(seriesName, opts) {
        return seriesName + ":  " + opts.w.globals.series[opts.seriesIndex]
      },
      itemMargin: { horizontal: 1 }
    }
  }
  var apexRadialBarChart = new ApexCharts(
    document.querySelector('#apexRadialBarChart'),
    apexRadialBarChartOptions
  );
  apexRadialBarChart.render();
&lt;/script&gt;