<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('applicants', function (Blueprint $table) {
            $table->bigIncrements("id");
            $table->unsignedBigInteger('lead_id')->unsigned();
            $table->unsignedBigInteger("district_id");
            $table->unsignedBigInteger("industry_id")->unsiged();

            $table->string("fullname");
            $table->string("email")->unique();
            $table->string("phone");
            $table->string("applicant_code")->nullable();
            $table->string("lead_code");
            $table->enum('payment_state',['paid','unpaid'])->default('unpaid');
            $table->string("status");

            $table->foreign('lead_id')->references('id')->on('leads')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('district_id')->references('id')->on('districts')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('industry_id')->references('id')->on('industries')->onDelete('cascade')->onUpdate('cascade');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('applicants');
    }
};
