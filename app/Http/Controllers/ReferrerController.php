<?php

namespace App\Http\Controllers;

use App\Models\referrer;
use Illuminate\Http\Request;

class ReferrerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(referrer $referrer)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(referrer $referrer)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, referrer $referrer)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(referrer $referrer)
    {
        //
    }
}
