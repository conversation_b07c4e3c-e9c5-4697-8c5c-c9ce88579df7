<?php

namespace App\Http\Controllers;

use App\Models\Industries;
use Illuminate\Http\Request;

class IndustriesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Industries $industries)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Industries $industries)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Industries $industries)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Industries $industries)
    {
        //
    }
}
