<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ApplicantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
			'lead_id' => 'required',
			'district_id' => 'required',
			'industry_id' => 'required',
			'fullname' => 'required|string',
			'email' => 'required|string',
			'phone' => 'required|string',
			'applicant_code' => 'string',
			'lead_code' => 'required|string',
			'payment_state' => 'required',
			'status' => 'required|string',
        ];
    }
}
