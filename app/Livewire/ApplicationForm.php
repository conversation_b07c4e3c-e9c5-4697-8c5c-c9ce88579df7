<?php

namespace App\Livewire;

use App\Mail\ApplicantCodeEmail;
use App\Models\Lead;
use Livewire\Component;
use App\Models\Applicant;
use App\Models\Industries;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Redirect;

class ApplicationForm extends Component
{

    public $industry = '';
    public $full_name = '';
    public $email = '';
    public $phone = '';
    public $referral_code = '';


    public function render()
    {
        $industries = Industries::select('id', 'name')->orderBy('name')->get();
        return view('livewire.application-form', ['industries' => $industries]);
    }

    protected $rules = [
        'industry' => 'required',
        'full_name' => 'required|string',
        'email' => 'required|string|unique:applicants',
        'phone' => 'required|string|unique:applicants',
        'referral_code' => 'required|string',
    ];

    public function mount()
    {

    }

    public function submit()
    {
        $this->validate();

        $lead = Lead::select('id', 'district_id')->where('lead_code', $this->referral_code)->first();
        $lead_id = $lead->id;
        $district = $lead->district_id;
        $applicant_code = $this->generateUniqueCode();

        Applicant::create([
            'lead_id' => $lead_id,
            'district_id' => $district,
            'industry_id' => $this->industry,
            'fullname' => $this->full_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'applicant_code' => $applicant_code,
            'lead_code' => $this->referral_code,
            'status' => 'pending',
        ]);



        $mailData = [
            'applicant_code' => $applicant_code
        ];

        Mail::to($this->email)->send(new ApplicantCodeEmail($mailData));

         $this->reset();

        return redirect()->route('applicant.returning-applicant')
            ->with('success', 'Congrat! an applicant code has been sent to your email, Check your email, copy and enter the code to continue. If you cannot find the email, check your spam folder.');

    }



    public function generateUniqueCode()
    {

        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersNumber = strlen($characters);
        $codeLength = 8;

        $code = '';

        while (strlen($code) < 8) {
            $position = rand(0, $charactersNumber - 1);
            $character = $characters[$position];
            $code = $code . $character;
        }

        if (Applicant::where('applicant_code', $code)->exists()) {
            $this->generateUniqueCode();
        }

        return $code;

    }
}
