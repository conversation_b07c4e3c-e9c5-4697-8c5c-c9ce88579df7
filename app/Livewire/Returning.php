<?php

namespace App\Livewire;

use App\Models\Applicant;
use App\Models\Payment;
use Livewire\Component;

class Returning extends Component
{
    public $applicant_code = '';
    public $applicant;

    public function rules(): array
    {
        return [
            'applicant_code' => 'required|string',
        ];
    }

    public function mount()
    {
        // $this->applicant = Applicant::select('id','lead_id','applicant_code','payment_state')->where('applicant_code',  $$this->applicant_code)->first();
    }

    public function render()
    {
        return view('livewire.returning');
    }

    public function submit()
    {
        $this->validate();

        $applicant_code = stripslashes(strip_tags(trim($this->applicant_code )));
        $code = Applicant::where('applicant_code', $applicant_code)->first();

       if( !$code){
            session()->flash('message', 'Invalid Applicant Code');
            return redirect()->route('applicant.returning-applicant');
        }else{
            // Store data in session
            session(['applicant_data' => $code]);
            return redirect()->route('applicant.recruitment-payment', ['id' => $code->id]);

            //  if ($applicant->payment_state != 'paid') {
            //     // $code = $applicant->applicant_code;
            //         return redirect()->route('applicant.recruitment-payment', ['id'=> $applicant->id,''=> $code->payment_state ]);
            // }
        }



        // $payment = Payment::where('applicant_id', $applicant->id)->first();
        // if( $applicant->applicant_code !=  $applicant_code ){
        //     session()->flash('message', 'Invalid Applicant Code');
        //     return redirect()->route('applicant.returning-applicant');
        // }else{
        //     if($payment->state == 'paid' && $payment->amount == '2000'){
        //         return redirect()->route('dashboard');
        //     }else if($payment->amount < '2000'){
        //          return redirect()->route('returning-applicant')
        //          ->with('warning', 'Ooops! you have not paid the full amoount of GHC2000.00. Please make  the additional payment of GHC'. number_format(2000 - $payment->amount, 2) .'.00 to continue');
        //     }else{
        //         return redirect()->route('applicant.recruitment-payment')->with(['payment' => $payment]);

        //     }
        // }

        // return redirect('/');
    }
}
