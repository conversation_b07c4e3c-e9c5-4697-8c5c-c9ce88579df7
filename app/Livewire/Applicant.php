<?php

namespace App\Livewire;

use App\Models\Applicant;
use App\Models\Lead;
use Livewire\Component;

class ApplicantForm extends Component
{



    public $lead_id = '';
    public $district = '';
    public $industry = '';
    public $fullname = '';
    public $email = '';
    public $phone = '';
    public $applicant_code = '';
    public $lead_code = '';
    public $payment_state = '';
    public $status = '';

    public function mount(Applicant $applicantModel)
    {
        $this->lead_id = Lead::select('id')->where('lead_code', $this->lead_id)->first()->id;

    }

    public function rules(): array
    {
        return [
            'district' => 'required',
            'industry' => 'required',
            'fullname' => 'required|string',
            'email' => 'required|string',
            'phone' => 'required|string',
            'referral_code' => 'required|string',
        ];
    }

    public function submit()
    {

        $this->validate();

        Applicant::create([
            'lead_id' => $this->lead_id,
            'district_id' => $this->district_id,
            'industry_id' => $this->industry_id,
            'fullname' => $this->fullname,
            'email' => $this->email,
            'phone' => $this->phone,
            'applicant_code' => $this->applicant_code,
            'lead_code' => $this->lead_code,
            'status' => $this->status,
        ]);

        $this->reset();
    }


    public function render()
    {
        return view('welcome');
    }


    public function generateUniqueCode()
    {

        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersNumber = strlen($characters);
        $codeLength = 6;

        $code = '';

        while (strlen($code) < 6) {
            $position = rand(0, $charactersNumber - 1);
            $character = $characters[$position];
            $code = $code . $character;
        }

        if (Applicant::where('applicant_code', $code)->exists()) {
            $this->generateUniqueCode();
        }

        return $code;

    }


}
