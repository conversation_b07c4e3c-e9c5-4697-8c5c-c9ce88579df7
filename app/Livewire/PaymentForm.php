<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Applicant;
// use App\Models\Applicant;

class PaymentForm extends Component
{
    public $applicantId;
    public $applicant;
    public function mount($id)
    {
        $this->applicantId = $id;

        // Optionally get additional data from session
        $sessionData = session('applicant_data');
        if ($sessionData) {
           $this->applicant  = $sessionData;
        }
    }

    public function render()
    {
        return view('livewire.payment-form', ['applicant' => $this->applicant]);
    }
}
