<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;

class Lead extends Model
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'district_id',
        'fullname',
        'email',
        'phone',
        'lead_code',
    ];

    public function district() {
        return $this->belongsTo(Districts::class);
    }

}
