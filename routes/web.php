<?php

use App\Http\Controllers\ApplicantController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\WelcomeController;
use App\Livewire\PaymentForm;
use Illuminate\Support\Facades\Route;


Route::get('/', [WelcomeController::class, 'index']);


Route::prefix('applicant')->name('applicant.')->group(function () {
    Route::get('returning-applicant', [ApplicantController::class, 'index'])->name('returning-applicant');
    Route::get('recruitment-payment/{id}', [PaymentController::class, 'index'])->name('recruitment-payment');
    Route::post('payment', [PaymentController::class, 'initialize'])->name('payment.initialize');
    Route::get('payment/callback', [PaymentController::class, 'callback'])->name('payment.callback');
});



Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth'])->name('dashboard');

Route::middleware(['auth', 'role:user'])->namespace('Student')->prefix('student')->name('student.')->group(function () {
    Route::get('/dashboard', 'DashboardController@index')->name('dashboard');
    Route::resource('/profile', 'ProfileController', ['except' => 'show', 'update']);
    Route::resource('/account', 'UserController');
});


// Route::middleware('auth')->group(function () {
//     Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
//     Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
//     Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
// });

require __DIR__.'/auth.php';

 Auth::routes([

  'register' => false, // Register Routes...

//   'reset' => false, // Reset Password Routes...

  'verify' => false, // Email Verification Routes...

]);

